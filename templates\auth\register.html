{% extends "auth/base.html" %}

{% block title %}注册 - 8D报告协作系统{% endblock %}

{% block page_title %}用户注册{% endblock %}
{% block page_subtitle %}创建您的账号开始使用{% endblock %}

{% block content %}
<form method="POST" novalidate>
    {{ form.hidden_tag() }}
    
    <div class="form-group">
        {{ form.username.label(class="form-label") }}
        {{ form.username(class="form-control" + (" error" if form.username.errors else ""), placeholder="请输入用户名") }}
        {% if form.username.errors %}
            {% for error in form.username.errors %}
                <div class="field-error">{{ error }}</div>
            {% endfor %}
        {% endif %}
    </div>
    
    <div class="form-group">
        {{ form.real_name.label(class="form-label") }}
        {{ form.real_name(class="form-control" + (" error" if form.real_name.errors else ""), placeholder="请输入真实姓名（可选）") }}
        {% if form.real_name.errors %}
            {% for error in form.real_name.errors %}
                <div class="field-error">{{ error }}</div>
            {% endfor %}
        {% endif %}
    </div>
    
    <div class="form-group">
        {{ form.email.label(class="form-label") }}
        {{ form.email(class="form-control" + (" error" if form.email.errors else ""), placeholder="请输入邮箱地址（可选）") }}
        {% if form.email.errors %}
            {% for error in form.email.errors %}
                <div class="field-error">{{ error }}</div>
            {% endfor %}
        {% endif %}
    </div>
    
    <div class="form-group">
        {{ form.department.label(class="form-label") }}
        {{ form.department(class="form-control" + (" error" if form.department.errors else ""), placeholder="请输入部门（可选）") }}
        {% if form.department.errors %}
            {% for error in form.department.errors %}
                <div class="field-error">{{ error }}</div>
            {% endfor %}
        {% endif %}
    </div>
    
    <div class="form-group">
        {{ form.position.label(class="form-label") }}
        {{ form.position(class="form-control" + (" error" if form.position.errors else ""), placeholder="请输入职位（可选）") }}
        {% if form.position.errors %}
            {% for error in form.position.errors %}
                <div class="field-error">{{ error }}</div>
            {% endfor %}
        {% endif %}
    </div>
    
    <div class="form-group">
        {{ form.password.label(class="form-label") }}
        {{ form.password(class="form-control" + (" error" if form.password.errors else ""), placeholder="请输入密码（至少6位）") }}
        {% if form.password.errors %}
            {% for error in form.password.errors %}
                <div class="field-error">{{ error }}</div>
            {% endfor %}
        {% endif %}
    </div>
    
    <div class="form-group">
        {{ form.password2.label(class="form-label") }}
        {{ form.password2(class="form-control" + (" error" if form.password2.errors else ""), placeholder="请再次输入密码") }}
        {% if form.password2.errors %}
            {% for error in form.password2.errors %}
                <div class="field-error">{{ error }}</div>
            {% endfor %}
        {% endif %}
    </div>
    
    {{ form.submit(class="btn btn-primary") }}
</form>

<div class="auth-links">
    <p>已有账号？ <a href="{{ url_for('auth.login') }}">立即登录</a></p>
</div>

<script>
// 自动聚焦到第一个输入框
document.addEventListener('DOMContentLoaded', function() {
    const firstInput = document.querySelector('.form-control');
    if (firstInput) {
        firstInput.focus();
    }
});

// 密码强度提示
document.addEventListener('DOMContentLoaded', function() {
    const passwordInput = document.getElementById('password');
    if (passwordInput) {
        passwordInput.addEventListener('input', function() {
            const password = this.value;
            let strength = 0;
            
            if (password.length >= 6) strength++;
            if (password.match(/[a-z]/)) strength++;
            if (password.match(/[A-Z]/)) strength++;
            if (password.match(/[0-9]/)) strength++;
            if (password.match(/[^a-zA-Z0-9]/)) strength++;
            
            // 这里可以添加密码强度指示器
        });
    }
});
</script>
{% endblock %}