"""
表单定义
使用Flask-WTF处理用户登录、注册等表单
"""

from flask_wtf import FlaskForm
from wtforms import StringField, PasswordField, BooleanField, SubmitField, TextAreaField
from wtforms.validators import DataRequired, Email, Optional, Length, EqualTo


class LoginForm(FlaskForm):
    """登录表单"""
    username = StringField('用户名', validators=[
        DataRequired(message='请输入用户名'),
        Length(min=3, max=80, message='用户名长度应在3-80个字符之间')
    ])
    password = PasswordField('密码', validators=[
        DataRequired(message='请输入密码')
    ])
    remember_me = BooleanField('记住我')
    submit = SubmitField('登录')


class RegisterForm(FlaskForm):
    """注册表单"""
    username = StringField('用户名', validators=[
        DataRequired(message='请输入用户名'),
        Length(min=3, max=80, message='用户名长度应在3-80个字符之间')
    ])
    email = StringField('邮箱', validators=[
        Optional(),
        Email(message='请输入有效的邮箱地址')
    ])
    real_name = StringField('真实姓名', validators=[
        Optional(),
        Length(max=100, message='姓名长度不能超过100个字符')
    ])
    department = StringField('部门', validators=[
        Optional(),
        Length(max=100, message='部门名称长度不能超过100个字符')
    ])
    position = StringField('职位', validators=[
        Optional(),
        Length(max=100, message='职位名称长度不能超过100个字符')
    ])
    password = PasswordField('密码', validators=[
        DataRequired(message='请输入密码'),
        Length(min=6, message='密码长度至少6个字符')
    ])
    password2 = PasswordField('确认密码', validators=[
        DataRequired(message='请确认密码'),
        EqualTo('password', message='两次输入的密码不一致')
    ])
    submit = SubmitField('注册')


class ProfileForm(FlaskForm):
    """用户资料编辑表单"""
    real_name = StringField('真实姓名', validators=[
        Optional(),
        Length(max=100, message='姓名长度不能超过100个字符')
    ])
    email = StringField('邮箱', validators=[
        Optional(),
        Email(message='请输入有效的邮箱地址')
    ])
    department = StringField('部门', validators=[
        Optional(),
        Length(max=100, message='部门名称长度不能超过100个字符')
    ])
    position = StringField('职位', validators=[
        Optional(),
        Length(max=100, message='职位名称长度不能超过100个字符')
    ])
    submit = SubmitField('更新资料')