* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
    padding-top: 20px; /* 减少顶部空白 */
    padding-left: 270px; /* 为左侧标签栏留出空间（250px + 20px） */
    padding-right: 120px; /* 为右侧导航留出空间 */
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.15);
    overflow: hidden;
}

.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 100%;
}

.header-left {
    display: flex;
    align-items: center;
    flex: 1;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
}

.header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* 页面logo样式 */
.header-title-with-logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.header-logo {
    width: 48px;
    height: 48px;
    object-fit: contain;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
    padding: 4px;
}

.header-title-text {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 20px;
        align-items: center;
    }
    
    .header-left {
        justify-content: center;
    }
    
    .header-title-with-logo {
        flex-direction: column;
        gap: 10px;
    }

    .header-logo {
        width: 40px;
        height: 40px;
    }
    
    .header-title-text {
        align-items: center;
    }
    
    .header-right {
        justify-content: center;
        flex-wrap: wrap;
    }
}

.form-container {
    padding: 40px;
}

.form-section {
    margin-bottom: 40px;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    overflow: hidden;
}

.section-header {
    background: #f8f9fa;
    padding: 20px;
    border-bottom: 1px solid #e1e5e9;
}

/* 表单章节标题样式 - 只在表单内容区域生效 */
.form-container .section-title {
    font-size: 1.7rem;
    color: #667eea;
    font-weight: 700;
    letter-spacing: 0.5px;
    line-height: 1.2;
    margin-bottom: 16px;
    margin-top: 0;
    padding: 0;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .form-container .section-title {
        font-size: 1.2rem;
    }
}

/* 右侧导航区域的标题样式 */
.right-navigation .section-title {
    font-size: 12px;
    font-weight: 600;
    color: #6c757d;
    text-transform: uppercase;
    padding: 16px 16px 8px;
}

.section-desc {
    color: #6c757d;
    font-size: 0.95rem;
    font-weight: 400;
    line-height: 1.4;
    margin-top: 4px;
}

.section-content {
    padding: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #555;
    font-weight: 500;
}

.required {
    color: #dc3545;
    font-weight: bold;
}



/* 记录管理面板样式 */
.records-panel {
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    margin-bottom: 30px;
    overflow: hidden;
    position: fixed;
    top: 120px;
    right: 20px;
    width: 400px;
    max-height: 500px;
    z-index: 999;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
}

.records-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: white;
    border-bottom: 1px solid #e1e5e9;
}

.close-records-btn {
    background: none;
    border: none;
    font-size: 24px;
    color: #6c757d;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.close-records-btn:hover {
    background: #f8f9fa;
    color: #495057;
}

.records-header h3 {
    margin: 0;
    color: #333;
    font-size: 1.3rem;
}

.records-actions {
    display: flex;
    gap: 10px;
}

.record-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
    display: flex;
    align-items: center;
    gap: 5px;
}

.record-btn:hover {
    background: #5a6fd8;
}

.new-record-btn {
    background: #28a745;
}

.new-record-btn:hover {
    background: #218838;
}

.save-record-btn {
    background: #007bff;
}

.save-record-btn:hover {
    background: #0056b3;
}

.records-list {
    padding: 20px;
    max-height: 300px;
    overflow-y: auto;
}

.no-records {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 20px;
}

.record-item {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: box-shadow 0.3s;
}

.record-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.record-info {
    flex: 1;
}

.record-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.record-type {
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 3px;
    font-weight: normal;
}

.type-draft {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.type-submitted {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.record-meta {
    font-size: 12px;
    color: #6c757d;
}

.record-actions {
    display: flex;
    gap: 8px;
}

.record-action-btn {
    background: none;
    border: 1px solid #dee2e6;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s;
}

.load-btn {
    color: #007bff;
    border-color: #007bff;
}

.load-btn:hover {
    background: #007bff;
    color: white;
}

.delete-btn {
    color: #dc3545;
    border-color: #dc3545;
}

.delete-btn:hover {
    background: #dc3545;
    color: white;
}

/* 固定悬浮导航 */
.fixed-navigation {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 12px 20px;
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    justify-content: center;
    min-height: 60px;
}

.nav-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0;
    max-width: 1200px;
    width: 100%;
    padding: 0 20px;
}

.nav-top {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    gap: 30px;
}



.nav-items {
    display: flex;
    gap: 4px;
    padding: 0;
}



.nav-btn {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 10px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    min-width: 60px;
    text-align: center;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
}

.new-btn {
    background: rgba(40, 167, 69, 0.8);
    border-color: rgba(40, 167, 69, 0.9);
}

.new-btn:hover {
    background: rgba(40, 167, 69, 1);
}

.save-btn {
    background: rgba(0, 123, 255, 0.8);
    border-color: rgba(0, 123, 255, 0.9);
}

.save-btn:hover {
    background: rgba(0, 123, 255, 1);
}

.history-btn {
    background: rgba(108, 117, 125, 0.8);
    border-color: rgba(108, 117, 125, 0.9);
}

.history-btn:hover {
    background: rgba(108, 117, 125, 1);
}





.nav-item {
    display: flex;
    align-items: center;
    padding: 8px 10px;
    text-decoration: none;
    color: rgba(255, 255, 255, 0.8);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border-radius: 6px;
    min-width: 70px;
    justify-content: center;
    flex-direction: column;
    gap: 2px;
    position: relative;
    cursor: pointer;
    border: 1px solid transparent;
}

.nav-item:hover {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.nav-item.active:hover {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.95), rgba(255, 215, 0, 0.9));
    box-shadow: 0 6px 20px rgba(255, 193, 7, 0.5);
}

.nav-item.active {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.9), rgba(255, 215, 0, 0.8));
    color: #2c3e50;
    box-shadow: 0 4px 16px rgba(255, 193, 7, 0.4);
    transform: translateY(-2px);
    border: 1px solid rgba(255, 215, 0, 0.6);
}

.nav-item.active::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 90%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #f39c12, transparent);
    border-radius: 1px;
    animation: activeGlow 3s ease-in-out infinite alternate;
}

@keyframes activeGlow {
    0% {
        opacity: 0.7;
        box-shadow: 0 0 8px rgba(243, 156, 18, 0.4);
    }
    100% {
        opacity: 1;
        box-shadow: 0 0 12px rgba(243, 156, 18, 0.6);
    }
}

.nav-item.completed {
    background: rgba(16, 185, 129, 0.3);
    color: white;
}

.nav-item.completed:hover {
    background: rgba(16, 185, 129, 0.4);
}

.nav-number {
    display: inline-block;
    width: 24px;
    height: 24px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border-radius: 50%;
    text-align: center;
    line-height: 24px;
    font-size: 11px;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

/* 使用说明导航项的特殊样式 */
.nav-item[data-section="guide"] .nav-number {
    background: rgba(255, 193, 7, 0.3);
    font-size: 12px;
    line-height: 22px;
}

.nav-item[data-section="guide"].active .nav-number {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    box-shadow: 0 2px 8px rgba(243, 156, 18, 0.5);
    border: 1px solid rgba(230, 126, 34, 0.8);
    color: white;
}

.nav-item.active .nav-number {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
    box-shadow: 0 2px 8px rgba(243, 156, 18, 0.5);
    transform: scale(1.1);
    border: 1px solid rgba(230, 126, 34, 0.8);
}

.nav-item.completed .nav-number {
    background: #10b981;
    color: white;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.4);
}

.nav-title {
    font-size: 11px;
    line-height: 1.2;
    text-align: center;
    font-weight: 500;
    transition: all 0.3s ease;
}

.nav-item.active .nav-title {
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3);
    color: #2c3e50;
}

.nav-progress-mini {
    font-size: 10px;
    color: rgba(255, 255, 255, 0.9);
    background: rgba(0, 0, 0, 0.2);
    padding: 2px 6px;
    border-radius: 8px;
    margin-top: 2px;
    font-weight: 600;
    text-align: center;
    min-width: 30px;
    transition: all 0.3s ease;
}

.nav-item.completed .nav-progress-mini {
    background: rgba(16, 185, 129, 0.8);
    color: white;
    box-shadow: 0 1px 4px rgba(16, 185, 129, 0.3);
}

.nav-item.active .nav-progress-mini {
    background: linear-gradient(135deg, rgba(44, 62, 80, 0.9), rgba(52, 73, 94, 0.8));
    color: white;
    box-shadow: 0 1px 4px rgba(44, 62, 80, 0.4);
    transform: scale(1.05);
    border: 1px solid rgba(52, 73, 94, 0.6);
}

/* 表单主体布局 */
.form-main-container {
    margin-top: 0; /* 导航栏已经通过body padding处理 */
    /* 保持原有的单列布局 */
}

/* AI面板改为固定悬浮在右侧 */
.ai-panel {
    position: fixed;
    top: 140px; /* 避开导航栏 */
    right: 20px;
    width: 380px;
    max-height: calc(100vh - 160px);
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    display: flex;
    flex-direction: column;
    z-index: 1000;
    transform: translateX(100%);
    transition: transform 0.3s ease-in-out;
}

/* AI面板显示时的动画 */
.ai-panel.show {
    transform: translateX(0);
}

/* AI面板完全悬浮，不需要调整表单布局 */

.ai-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
    color: white;
    border-radius: 12px 12px 0 0;
}

.ai-panel-title {
    display: flex;
    align-items: center;
    gap: 10px;
}

.ai-icon {
    font-size: 1.5rem;
}

.ai-panel-title h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.ai-panel-actions {
    display: flex;
    gap: 8px;
}

.ai-panel-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s;
}

.ai-panel-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.ai-panel-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 20px;
    overflow: hidden;
}

/* AI状态提示 */
.ai-status {
    margin-bottom: 20px;
}

.status-message {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 16px;
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
    border-radius: 8px;
    font-size: 14px;
}

.status-icon {
    font-size: 16px;
}

/* 对话历史 */
.chat-history {
    flex: 1;
    overflow-y: auto;
    margin-bottom: 20px;
    padding-right: 8px;
}

.chat-history::-webkit-scrollbar {
    width: 6px;
}

.chat-history::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.chat-history::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chat-history::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.ai-message, .user-message {
    display: flex;
    margin-bottom: 16px;
    animation: fadeInUp 0.3s ease-out;
}

.user-message {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #4a90e2;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    flex-shrink: 0;
    margin-right: 12px;
}

.user-message .message-avatar {
    background: #6c757d;
    margin-right: 0;
    margin-left: 12px;
}

.message-content {
    flex: 1;
    background: white;
    padding: 12px 16px;
    border-radius: 12px;
    border: 1px solid #e1e5e9;
    font-size: 14px;
    line-height: 1.5;
}

.user-message .message-content {
    background: #4a90e2;
    color: white;
    border-color: #4a90e2;
}

.message-content p {
    margin: 0 0 8px 0;
}

.message-content p:last-child {
    margin-bottom: 0;
}

.message-content ul {
    margin: 8px 0;
    padding-left: 20px;
}

.message-content li {
    margin: 4px 0;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 快速建议 */
.quick-suggestions {
    margin-bottom: 20px;
}

.suggestions-title {
    font-size: 14px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 12px;
}

.suggestion-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.suggestion-btn {
    background: white;
    border: 1px solid #667eea;
    color: #667eea;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s;
    white-space: nowrap;
}

.suggestion-btn:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: translateY(-1px);
    border-color: transparent;
}

/* 输入区域 */
.chat-input-container {
    border-top: 1px solid #e1e5e9;
    padding-top: 16px;
}

.chat-input-wrapper {
    position: relative;
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
}

#chat-input {
    flex: 1;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 12px;
    font-size: 14px;
    resize: none;
    font-family: inherit;
    line-height: 1.4;
    transition: border-color 0.3s;
}

#chat-input:focus {
    outline: none;
    border-color: #4a90e2;
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.input-actions {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.voice-btn, .send-btn {
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 32px;
}

.voice-btn {
    background: #f8f9fa;
    color: #6c757d;
    padding: 6px 8px;
    border: 1px solid #e1e5e9;
}

.voice-btn:hover {
    background: #e9ecef;
    color: #495057;
}

.send-btn {
    background: #4a90e2;
    color: white;
    padding: 6px 12px;
    font-weight: 500;
}

.send-btn:hover {
    background: #357abd;
}

.send-btn:disabled {
    background: #e9ecef;
    color: #6c757d;
    cursor: not-allowed;
}

.input-hint {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #6c757d;
}

.char-count {
    font-weight: 500;
}





/* 生成文档按钮 */
.generate-docs-container {
    border-top: 1px solid #e1e5e9;
    padding-top: 16px;
    text-align: center;
}

.generate-docs-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    width: 100%;
    transition: all 0.3s;
}

.generate-docs-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.generate-docs-btn:disabled {
    background: #e9ecef;
    color: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.docs-hint {
    font-size: 12px;
    color: #6c757d;
    margin-top: 8px;
}

/* 加载动画 */
.loading-spinner, .loading-spinner-small {
    display: inline-block;
    width: 14px;
    height: 14px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-spinner {
    width: 16px;
    height: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 提交按钮状态 */
.submit-btn {
    position: relative;
}

.btn-loading {
    display: flex;
    align-items: center;
    gap: 8px;
}

/* AI助手浮动按钮 */
.ai-float-btn {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 999;
    animation: fadeInUp 0.3s ease-out;
}

.float-btn {
    background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 50px;
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(74, 144, 226, 0.3);
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    min-width: 60px;
    justify-content: center;
}

.float-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(74, 144, 226, 0.4);
    background: linear-gradient(135deg, #357abd 0%, #2c5aa0 100%);
}

.float-btn-icon {
    font-size: 18px;
    line-height: 1;
}

.float-btn-text {
    font-size: 13px;
    white-space: nowrap;
}

/* 浮动按钮响应式设计 */
@media (max-width: 768px) {
    .ai-float-btn {
        bottom: 20px;
        right: 20px;
    }
    
    .float-btn {
        padding: 10px 16px;
    }
    
    .float-btn-text {
        display: none;
    }
    
    .float-btn {
        min-width: 50px;
        border-radius: 50%;
        padding: 12px;
    }
}

/* 使用说明样式 */
.guide-content {
    line-height: 1.6;
}

.guide-content h3 {
    color: #333;
    margin-bottom: 20px;
    font-size: 1.3rem;
    border-bottom: 2px solid #667eea;
    padding-bottom: 10px;
}

.guide-section {
    margin-bottom: 25px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.guide-section h4 {
    color: #495057;
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.guide-section p {
    margin-bottom: 10px;
    color: #6c757d;
}

.guide-section ul {
    margin: 10px 0;
    padding-left: 20px;
}

.guide-section li {
    margin-bottom: 8px;
    color: #6c757d;
}

.guide-section li strong {
    color: #495057;
    font-weight: 600;
}

/* 表单内容区域 */
.form-content {
    width: 100%;
}

/* 折叠面板样式 */
.form-section {
    transition: all 0.3s ease;
}

.section-header {
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e1e5e9;
    transition: background-color 0.3s ease;
}

.section-header:hover {
    background: #e9ecef;
}

.section-title-container {
    flex: 1;
}

.section-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.completion-badge {
    background: #6c757d;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    min-width: 35px;
    text-align: center;
}

.completion-badge.completed {
    background: #28a745;
}

.completion-badge.partial {
    background: #ffc107;
    color: #000;
}

.collapse-btn {
    background: none;
    border: 1px solid #dee2e6;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #6c757d;
    font-weight: bold;
}

.collapse-btn:hover {
    background: #e9ecef;
    border-color: #adb5bd;
}

.collapse-icon {
    font-size: 18px;
    line-height: 1;
    transition: transform 0.3s ease;
}

.form-section.collapsed .collapse-icon {
    transform: rotate(90deg);
}

.form-section.collapsed .section-content {
    display: none;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    body {
        padding-top: 130px;
    }
    
    .fixed-navigation {
        padding: 8px 15px;
        min-height: 70px;
    }
    

    
    .nav-items {
        gap: 2px;
    }
    
    .nav-item {
        padding: 6px 6px;
        min-width: 60px;
    }
    
    .nav-number {
        width: 20px;
        height: 20px;
        line-height: 20px;
        font-size: 10px;
    }
    
    .nav-title {
        font-size: 10px;
    }
    
    .nav-progress-mini {
        font-size: 9px;
        padding: 1px 4px;
        min-width: 25px;
    }
    
    .nav-btn {
        padding: 3px 6px;
        font-size: 9px;
        min-width: 50px;
        height: 20px;
    }
    
    .records-panel {
        width: 350px;
        top: 120px;
    }
}

@media (max-width: 768px) {
    body {
        padding-top: 220px; /* 为导航栏和版本标签栏留出空间 */
    }
    
    .fixed-navigation {
        padding: 8px;
        min-height: auto;
    }
    
    .nav-top {
        flex-direction: column;
        gap: 8px;
    }
    
    .nav-items {
        justify-content: center;
        flex-wrap: wrap;
        gap: 3px;
        order: 2;
    }

    .nav-item {
        padding: 4px 4px;
        min-width: 45px;
        flex: 1;
        max-width: calc(25% - 2px);
    }
    
    .nav-number {
        width: 18px;
        height: 18px;
        line-height: 18px;
        font-size: 9px;
    }
    
    .nav-title {
        font-size: 9px;
    }
    
    .nav-progress-mini {
        font-size: 8px;
        padding: 1px 4px;
        min-width: 25px;
    }
    

    
    .nav-btn {
        padding: 3px 6px;
        font-size: 9px;
        min-width: 45px;
        height: 20px;
    }
    
    .records-panel {
        width: calc(100% - 20px);
        right: 10px;
        left: 10px;
        top: 160px;
        max-height: 400px;
    }
}

@media (max-width: 480px) {
    .nav-item {
        max-width: calc(33.33% - 2px);
        min-width: 35px;
    }
    
    .nav-items {
        gap: 1px;
    }
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 0;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    line-height: 20px;
    transition: border-color 0.3s;
    height: 38px;
    box-sizing: border-box;
    vertical-align: top;
    font-family: inherit;
    padding-left: 12px;
    padding-right: 12px;
    padding-top: 8px;
    padding-bottom: 8px;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
    resize: none;
    min-height: 38px;
    overflow: hidden;
    transition: height 0.1s ease;
}

/* 强制所有输入框统一高度 */
.form-group input[type="text"],
.form-group input[type="date"],
.form-group input {
    height: 38px !important;
    min-height: 38px !important;
    max-height: 38px !important;
    line-height: 20px !important;
    padding: 8px 12px !important;
    box-sizing: border-box !important;
}

/* 日期输入框特殊处理 */
.form-group input[type="date"] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

/* Chrome/Safari 日期输入框特殊处理 */
.form-group input[type="date"]::-webkit-calendar-picker-indicator {
    height: 20px;
    width: 20px;
    cursor: pointer;
}



.form-group select {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath fill='%23666' d='M8 12l-4-4h8z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 12px;
    padding-right: 28px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-row-3 {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 20px;
}

.form-row-4 {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 2fr;
    gap: 15px;
}

.form-row-d5 {
    display: grid;
    grid-template-columns: 4fr 0.8fr 0.8fr;
    gap: 15px;
}

.form-row-d6 {
    display: grid;
    grid-template-columns: 3fr 0.8fr 0.8fr 3fr;
    gap: 15px;
}

.form-row-d7 {
    display: grid;
    grid-template-columns: 4fr 0.8fr 0.8fr;
    gap: 15px;
}

.form-row-d8 {
    display: grid;
    grid-template-columns: 2.5fr 1fr 1fr;
    gap: 20px;
}

.form-row-d0 {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 15px;
}

.form-row-d3-top {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 15px;
}

.form-row-d4-analysis {
    display: grid;
    grid-template-columns: 2fr 0.5fr 2fr 80px;
    gap: 15px;
    align-items: start;
}

.member-group,
.measure-group,
.verification-group,
.prevention-group,
.analysis-group {
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 15px;
    background: #f8f9fa;
}

.group-title {
    font-weight: 600;
    color: #667eea;
    margin-bottom: 10px;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.submit-container {
    display: flex;
    gap: 20px;
    justify-content: center;
    margin-top: 40px;
    padding-top: 30px;
    border-top: 1px solid #e1e5e9;
}

.action-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    border: none;
    padding: 15px 30px;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 180px;
    justify-content: center;
}

.ai-optimize-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.ai-optimize-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
}

.direct-generate-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.direct-generate-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(40, 167, 69, 0.4);
}

.action-btn .btn-icon {
    font-size: 1.2rem;
}

.action-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}



/* 保留原有的submit-btn样式以防兼容性问题 */
.submit-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 15px 60px;
    border-radius: 50px;
    font-size: 1.2rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

/* 添加成员按钮样式 */
.add-member-section {
    text-align: center;
    margin-top: 20px;
    padding: 20px;
    border: 2px dashed #667eea;
    border-radius: 8px;
    background: #f8f9ff;
}

.add-member-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 10px;
}

.add-member-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.member-counter {
    color: #667eea;
    font-weight: 500;
    font-size: 0.9rem;
}

/* 删除成员按钮样式 */
.remove-member-btn {
    background: #dc3545;
    color: white;
    border: none;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    cursor: pointer;
    margin-left: 10px;
    transition: all 0.3s ease;
}

.remove-member-btn:hover {
    background: #c82333;
    transform: scale(1.05);
}



/* 原因分析相关样式 */
.cause-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.add-cause-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.add-cause-btn:hover {
    background: #218838;
    transform: scale(1.05);
}

.cause-input-group {
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.cause-input-group textarea {
    flex: 1;
}

.inline-actions {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 100%;
    min-height: 65px;
}

.remove-cause-btn {
    background: #dc3545;
    color: white;
    border: none;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 40px;
}

.remove-cause-btn:hover {
    background: #c82333;
    transform: scale(1.05);
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
}

.alert {
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;
}

.alert-success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.alert-error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}



@media (max-width: 768px) {
    .form-row,
    .form-row-3,
    .form-row-d4-analysis,
    .form-row-4,
    .form-row-d0,
    .form-row-d3-top,
    .form-row-d8 {
        grid-template-columns: 1fr;
    }
    
    .inline-actions {
        padding-top: 10px;
        justify-content: center;
    }
}

@media (max-width: 1024px) and (min-width: 769px) {
    .form-row-4 {
        grid-template-columns: 1fr 1fr;
        gap: 15px;
    }
    
    .form-row-4 .form-group:nth-child(3),
    .form-row-4 .form-group:nth-child(4) {
        grid-column: span 1;
    }
    
    .form-row-d0 {
        grid-template-columns: 1fr 1fr;
        gap: 15px;
    }
    
    .form-row-d0 .form-group:first-child {
        grid-column: span 2;
    }
    
    .form-row-d3-top {
        grid-template-columns: 1fr;
        gap: 15px;
    }
}

/* AI面板响应式设计 */
@media (max-width: 1400px) {
    .ai-panel {
        width: 350px;
    }
}

@media (max-width: 1200px) {
    .ai-panel {
        width: 320px;
    }
}

@media (max-width: 1024px) {
    .ai-panel {
        position: fixed;
        top: 120px;
        right: 10px;
        left: 10px;
        width: auto;
        max-height: 60vh;
        transform: translateY(100%);
    }
    
    .ai-panel.show {
        transform: translateY(0);
    }
}

@media (max-width: 768px) {
    .ai-panel {
        top: 100px;
        max-height: 50vh;
    }
    
    .ai-panel-content {
        padding: 16px;
    }
    
    .suggestion-buttons {
        flex-direction: column;
    }
    
    .suggestion-btn {
        white-space: normal;
        text-align: left;
    }
    
    .ai-panel-header {
        padding: 15px;
    }
    
    .ai-panel-title h3 {
        font-size: 1rem;
    }
    
    .chat-input-wrapper {
        flex-direction: column;
        gap: 8px;
    }
    
    .input-actions {
        flex-direction: row;
        justify-content: space-between;
    }
}

/* 修改意见显示样式 */
.modification-notes {
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%);
    border: 1px solid #d1e5ff;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
}

.modification-notes h4 {
    color: #2563eb;
    font-size: 1.1rem;
    margin-bottom: 12px;
    font-weight: 600;
}

.notes-content {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 12px;
}

.notes-content p {
    color: #374151;
    line-height: 1.6;
    margin: 0;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.notes-meta {
    display: flex;
    gap: 16px;
    font-size: 0.9rem;
    color: #6b7280;
}

.notes-time,
.notes-author {
    display: flex;
    align-items: center;
}

.notes-time::before {
    content: '🕐';
    margin-right: 4px;
}

.notes-author::before {
    content: '👤';
    margin-right: 4px;
}

.no-modifications {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    color: #64748b;
    font-style: italic;
}

.no-modifications p {
    margin: 0;
}

/* AI优化模态框样式 - 与侧边栏风格统一 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    animation: fadeIn 0.3s ease;
    backdrop-filter: blur(4px);
}

.modal-content {
    background: white;
    border-radius: 16px;
    padding: 0;
    max-width: 520px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2), 0 8px 32px rgba(102, 126, 234, 0.15);
    animation: slideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 24px 28px;
    border-bottom: none;
    position: relative;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.modal-header h3 {
    margin: 0;
    font-size: 1.4rem;
    font-weight: 600;
    letter-spacing: 0.3px;
}

.modal-close-btn {
    position: absolute;
    top: 50%;
    right: 24px;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    font-size: 20px;
    cursor: pointer;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.modal-close-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-50%) scale(1.05);
}

.modal-body {
    padding: 28px;
    background: #f8f9fa;
}

.modal-body label {
    display: block;
    margin-bottom: 16px;
    font-weight: 600;
    color: #374151;
    font-size: 15px;
    line-height: 1.5;
}

.modal-body p {
    margin-bottom: 20px;
    font-weight: 600;
    color: #374151;
    font-size: 15px;
    line-height: 1.6;
}

.modal-body textarea {
    width: 100%;
    min-height: 140px;
    padding: 16px;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    font-size: 14px;
    line-height: 1.6;
    resize: vertical;
    transition: all 0.3s ease;
    font-family: inherit;
    background: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    margin-top: 4px;
}

.modal-body textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1), 0 4px 12px rgba(102, 126, 234, 0.15);
    transform: translateY(-1px);
}

.char-count-container {
    text-align: right;
    margin-top: 12px;
    font-size: 13px;
    color: #6c757d;
    font-weight: 500;
}

.modal-footer {
    padding: 24px 28px;
    background: white;
    border-top: 1px solid #e1e5e9;
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.modal-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 90px;
    letter-spacing: 0.3px;
}

.cancel-btn {
    background: rgba(108, 117, 125, 0.1);
    color: #6c757d;
    border: 1px solid #d1d5db;
}

.cancel-btn:hover {
    background: rgba(108, 117, 125, 0.15);
    border-color: #9ca3af;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(108, 117, 125, 0.2);
}

.confirm-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: 1px solid rgba(102, 126, 234, 0.3);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.confirm-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    filter: brightness(1.05);
}

.confirm-btn:active {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 20px;
        border-radius: 12px;
    }
    
    .modal-header {
        padding: 20px;
    }
    
    .modal-header h3 {
        font-size: 1.2rem;
    }
    
    .modal-close-btn {
        right: 20px;
        width: 32px;
        height: 32px;
        font-size: 18px;
    }
    
    .modal-body {
        padding: 20px;
    }
    
    .modal-footer {
        padding: 20px;
        flex-direction: column;
    }
    
    .modal-btn {
        width: 100%;
        padding: 14px 24px;
    }
}

/* 深色模式适配（如果需要） */
@media (prefers-color-scheme: dark) {
    .modal-content {
        background: #1f2937;
        border-color: rgba(255, 255, 255, 0.1);
    }
    
    .modal-body {
        background: #111827;
    }
    
    .modal-body label {
        color: #f3f4f6;
    }
    
    .modal-body textarea {
        background: #1f2937;
        border-color: #374151;
        color: #f3f4f6;
    }
    
    .modal-footer {
        background: #1f2937;
        border-color: #374151;
    }
}



.help-icon {
    font-size: 18px;
    line-height: 1;
    font-weight: bold;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.help-text {
    font-size: 13px;
    white-space: nowrap;
}

/* 使用说明模态窗口样式 */
.help-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10001;
    display: none;
    animation: fadeIn 0.3s ease;
}

.help-modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.help-modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
}

.help-modal-content {
    position: relative;
    background: white;
    border-radius: 16px;
    max-width: 800px;
    width: 90%;
    max-height: 85vh;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
    animation: slideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
    display: flex;
    flex-direction: column;
}

.help-modal-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 24px 28px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
}

.help-modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    letter-spacing: 0.3px;
}

.help-modal-close {
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    font-size: 24px;
    cursor: pointer;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.help-modal-close:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    transform: scale(1.05);
}

.help-modal-body {
    flex: 1;
    overflow-y: auto;
    padding: 32px;
    background: #f8f9fa;
}

.help-section {
    margin-bottom: 32px;
}

.help-section h3 {
    color: #28a745;
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 16px;
    border-bottom: 2px solid #28a745;
    padding-bottom: 8px;
}

.help-section p {
    color: #495057;
    line-height: 1.6;
    margin-bottom: 16px;
    font-size: 15px;
}

.help-step {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 16px;
    border-left: 4px solid #28a745;
}

.help-step h4 {
    color: #28a745;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 12px;
}

.help-step p {
    margin-bottom: 12px;
    color: #495057;
}

.help-step ul, .help-section ul {
    margin: 12px 0;
    padding-left: 24px;
}

.help-step li, .help-section li {
    margin-bottom: 8px;
    color: #495057;
    line-height: 1.5;
}

.help-step li strong, .help-section li strong {
    color: #28a745;
    font-weight: 600;
}

.help-section ol {
    margin: 12px 0;
    padding-left: 24px;
}

.help-section ol li {
    margin-bottom: 8px;
    color: #495057;
    line-height: 1.5;
}

.help-section ol li strong {
    color: #28a745;
    font-weight: 600;
}

.help-footer {
    text-align: center;
    padding: 24px;
    background: white;
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 16px 16px;
}

.help-footer p {
    margin: 0;
    color: #6c757d;
    font-style: italic;
    font-size: 16px;
    font-weight: 500;
}

.help-footer em {
    color: #28a745;
    font-weight: 600;
}



    .help-modal-content {
        width: 95%;
        max-height: 90vh;
        border-radius: 12px;
    }

    .help-modal-header {
        padding: 20px;
    }

    .help-modal-header h2 {
        font-size: 1.3rem;
    }

    .help-modal-close {
        width: 36px;
        height: 36px;
        font-size: 20px;
    }

    .help-modal-body {
        padding: 24px 20px;
    }

    .help-section h3 {
        font-size: 1.2rem;
    }

    .help-step {
        padding: 16px;
    }

    .help-step h4 {
        font-size: 1rem;
    }
}

/* ==================== 用户信息和存储状态样式 ==================== */

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}



/* 存储状态指示器 */
.storage-status {
    position: relative;
    display: flex;
    align-items: center;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    backdrop-filter: blur(10px);
    cursor: pointer;
    transition: all 0.3s ease;
}

.storage-status:hover {
    background: rgba(255, 255, 255, 0.2);
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 6px;
}

.status-icon {
    font-size: 16px;
}

.status-text {
    color: white;
    font-size: 14px;
    font-weight: 500;
}

.status-details {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 8px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    padding: 12px;
    min-width: 160px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
}

.storage-status:hover .status-details {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 0;
}

.status-item .label {
    color: #666;
    font-size: 12px;
}

.status-item .value {
    color: #333;
    font-size: 12px;
    font-weight: 600;
}

/* 存储状态动画效果 */
.status-syncing .status-icon {
    animation: rotate 1s linear infinite;
}

.status-error .status-icon {
    color: #dc3545;
}

.status-success .status-icon {
    color: #28a745;
}

.status-warning {
    background: rgba(255, 193, 7, 0.15) !important;
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.status-warning:hover {
    background: rgba(255, 193, 7, 0.25) !important;
}

.status-warning .status-icon {
    color: #ffc107;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 用户信息区域 */
.user-info {
    position: relative;
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    cursor: pointer;
    transition: all 0.3s ease;
}

.user-info:hover {
    background: rgba(255, 255, 255, 0.2);
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.avatar-text {
    color: white;
    font-size: 16px;
    font-weight: 600;
    text-transform: uppercase;
}

.user-details {
    display: flex;
    flex-direction: column;
    min-width: 0;
}

.user-name {
    color: white;
    font-size: 14px;
    font-weight: 600;
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-role {
    color: rgba(255, 255, 255, 0.8);
    font-size: 12px;
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-menu {
    position: relative;
}

.user-menu-btn {
    width: 24px;
    height: 24px;
    border: none;
    background: transparent;
    color: white;
    cursor: pointer;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;
}

.user-menu-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.menu-icon {
    font-size: 16px;
    line-height: 1;
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 8px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    padding: 8px 0;
    min-width: 160px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
}

.user-menu.active .user-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 16px;
    color: #333;
    text-decoration: none;
    font-size: 14px;
    transition: background-color 0.2s ease;
}

.dropdown-item:hover {
    background: #f8f9fa;
    color: #333;
    text-decoration: none;
}

.logout-item:hover {
    background: #dc3545;
    color: white;
}

.item-icon {
    font-size: 16px;
    width: 20px;
    text-align: center;
}

.item-text {
    flex: 1;
}

.dropdown-divider {
    height: 1px;
    background: #e9ecef;
    margin: 8px 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .header-right {
        gap: 15px;
    }
    
    .user-details {
        display: none;
    }
    
    .storage-status .status-text {
        display: none;
    }
}

@media (max-width: 768px) {
    .header-right {
        gap: 10px;
    }
    
    .storage-status {
        padding: 6px 8px;
    }
    
    .user-info {
        padding: 6px;
    }
    
    .user-avatar {
        width: 32px;
        height: 32px;
    }
    
    .avatar-text {
        font-size: 14px;
    }
}