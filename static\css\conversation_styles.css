/**
 * 对话式8D报告系统 - 界面样式
 * 包含标签栏、对话历史、版本管理等组件样式
 */

/* ==================== 头部样式 ==================== */
.header-content {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.header-left h1 {
    font-size: 2.2rem;
    margin-bottom: 5px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-left p {
    font-size: 1rem;
    opacity: 0.9;
    margin: 0;
}



.header-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    padding: 10px 16px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.header-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.header-btn .btn-icon {
    font-size: 16px;
}



.conversation-history-btn {
    background: rgba(108, 117, 125, 0.8) !important;
    border-color: rgba(108, 117, 125, 0.9) !important;
}

.conversation-history-btn:hover {
    background: rgba(108, 117, 125, 1) !important;
}

/* ==================== 统一侧边栏样式 ==================== */
.unified-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: 280px;
    height: 100vh;
    background: #f8f9fa;
    border-right: 1px solid #e1e5e9;
    display: flex;
    flex-direction: column;
    z-index: 1000;
    transition: transform 0.3s ease;
    overflow: hidden;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}



/* 悬浮的展开按钮 */
.floating-expand-btn {
    position: fixed;
    top: 20px;
    left: 20px;
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 12px;
    color: white;
    font-size: 20px;
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
    z-index: 1001;
    display: none;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.floating-expand-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(102, 126, 234, 0.4);
}

/* 响应式设计：小屏幕时自动收纳侧边栏 */
@media (max-width: 1200px) {
    .unified-sidebar {
        width: 60px;
        overflow: visible;
    }

    .sidebar-header {
        padding: 8px;
        justify-content: center;
        flex-direction: column;
        gap: 6px;
    }

    .sidebar-logo {
        order: 1;
        justify-content: center;
    }

    .sidebar-logo-img {
        width: 28px;
        height: 28px;
    }

    .sidebar-help-btn-header {
        order: 2;
        width: 36px;
        height: 36px;
        border-radius: 8px;
        padding: 0;
        justify-content: center;
        min-width: auto;
    }

    .sidebar-help-btn-header .help-text {
        display: none;
    }

    .sidebar-help-btn-header .help-icon {
        font-size: 14px;
    }

    .sidebar-toggle-btn {
        display: flex;
        order: 3;
    }

    /* 收起状态下隐藏用户信息的文字部分 */
    .sidebar-user-section {
        padding: 8px;
    }

    .sidebar-user-info {
        padding: 4px;
        justify-content: center;
    }

    .sidebar-user-info .user-details {
        display: none;
    }

    .sidebar-user-info .user-menu {
        display: none;
    }

    .sidebar-user-info .user-avatar {
        width: 32px;
        height: 32px;
        font-size: 12px;
    }

    /* 收起状态下的保存状态指示器 */
    .sidebar-save-status {
        margin-top: 4px;
        padding: 4px;
        justify-content: center;
    }

    .sidebar-save-status .save-status-text {
        display: none;
    }

    .sidebar-save-status .save-status-icon {
        font-size: 16px;
    }

    .sidebar-top-actions {
        padding: 8px;
        gap: 4px;
        flex-direction: column;
    }

    .sidebar-top-actions .sidebar-action-btn {
        width: 44px;
        height: 44px;
        border-radius: 12px;
        padding: 0;
        justify-content: center;
        flex: none;
    }

    .sidebar-top-actions .sidebar-action-btn .btn-text {
        display: none;
    }

    .sidebar-top-actions .sidebar-action-btn .btn-icon {
        font-size: 16px;
    }

    .section-header-with-action {
        display: none;
    }

    .section-action-btn {
        display: none;
    }

    .new-conversation-section {
        padding: 8px;
    }





    .btn-icon {
        font-size: 20px;
    }

    /* 隐藏收纳状态下的其他内容 */
    .conversation-history-section,
    .version-history-section,
    .section-title {
        display: none;
    }

    /* 侧边栏底部按钮在收纳状态下的样式 */
    .sidebar-bottom-actions {
        padding: 8px;
        gap: 4px;
    }

    .sidebar-action-btn {
        width: 44px;
        height: 44px;
        border-radius: 12px;
        padding: 0;
        justify-content: center;
    }

    .sidebar-action-btn .btn-text {
        display: none;
    }

    .sidebar-action-btn .btn-icon {
        font-size: 16px;
    }

    body {
        padding-left: 80px;
    }
}

/* 侧边栏头部 */
.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: white;
    border-bottom: 1px solid #e1e5e9;
    flex-shrink: 0;
    gap: 8px;
}

/* 侧边栏logo */
.sidebar-logo {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex: 0 0 auto;
}

.sidebar-logo-img {
    width: 32px;
    height: 32px;
    object-fit: contain;
    border-radius: 6px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

/* 头部使用说明按钮 */
.sidebar-help-btn-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 6px 12px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    box-shadow: 0 2px 6px rgba(40, 167, 69, 0.2);
    flex: 0 0 auto;
    min-width: 120px;
}

.sidebar-help-btn-header:hover {
    background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
    transform: translateY(-1px);
    box-shadow: 0 3px 8px rgba(40, 167, 69, 0.3);
}

.sidebar-help-btn-header .help-icon {
    font-size: 12px;
    font-weight: bold;
}

.sidebar-help-btn-header .help-text {
    font-size: 12px;
}

.sidebar-toggle-btn {
    display: none; /* 默认隐藏，只在小屏幕时显示 */
    width: 44px;
    height: 44px;
    border: none;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    cursor: pointer;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
    font-size: 18px;
    color: white;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.sidebar-toggle-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}





/* 侧边栏用户信息区域 */
.sidebar-user-section {
    padding: 12px 16px;
    border-bottom: 1px solid #e5e7eb;
    flex-shrink: 0;
    background: #f8f9fa;
}

/* 侧边栏用户信息 */
.sidebar-user-info {
    position: relative;
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    backdrop-filter: blur(10px);
    cursor: pointer;
    transition: all 0.3s ease;
}

.sidebar-user-info:hover {
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.sidebar-user-info .user-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 14px;
    flex-shrink: 0;
    box-shadow: 0 2px 6px rgba(102, 126, 234, 0.3);
}

.sidebar-user-info .user-details {
    flex: 1;
    min-width: 0;
}

.sidebar-user-info .user-name {
    font-weight: 600;
    font-size: 13px;
    color: #1f2937;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.sidebar-user-info .user-role {
    font-size: 11px;
    color: #6b7280;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.sidebar-user-info .user-menu {
    position: relative;
}

.sidebar-user-info .user-menu-btn {
    width: 24px;
    height: 24px;
    border: none;
    background: transparent;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #6b7280;
}

.sidebar-user-info .user-menu-btn:hover {
    background: rgba(107, 114, 128, 0.1);
    color: #374151;
}

.sidebar-user-info .user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    min-width: 180px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1000;
}

.sidebar-user-info .user-menu:hover .user-dropdown,
.sidebar-user-info .user-menu.active .user-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.sidebar-user-info .dropdown-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    color: #374151;
    text-decoration: none;
    font-size: 12px;
    transition: all 0.2s ease;
}

.sidebar-user-info .dropdown-item:hover {
    background: #f3f4f6;
}

.sidebar-user-info .dropdown-item.logout-item:hover {
    background: #fef2f2;
    color: #dc2626;
}

.sidebar-user-info .dropdown-divider {
    height: 1px;
    background: #e5e7eb;
    margin: 4px 0;
}

/* 侧边栏保存状态指示器 */
.sidebar-save-status {
    margin-top: 8px;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    backdrop-filter: blur(10px);
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.sidebar-save-status:hover {
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.sidebar-save-status .save-status-content {
    display: flex;
    align-items: center;
    gap: 6px;
}

.sidebar-save-status .save-status-icon {
    font-size: 14px;
}

.sidebar-save-status .save-status-text {
    font-size: 12px;
    font-weight: 500;
    color: #374151;
}

/* 保存状态的不同状态样式 */
.sidebar-save-status.status-unsaved {
    background: rgba(251, 191, 36, 0.1);
    border-color: rgba(251, 191, 36, 0.3);
}

.sidebar-save-status.status-unsaved .save-status-text {
    color: #d97706;
}

.sidebar-save-status.status-saving {
    background: rgba(59, 130, 246, 0.1);
    border-color: rgba(59, 130, 246, 0.3);
}

.sidebar-save-status.status-saving .save-status-text {
    color: #2563eb;
}

.sidebar-save-status.status-saved {
    background: rgba(34, 197, 94, 0.1);
    border-color: rgba(34, 197, 94, 0.3);
}

.sidebar-save-status.status-saved .save-status-text {
    color: #16a34a;
}

.sidebar-save-status.status-error {
    background: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.3);
}

.sidebar-save-status.status-error .save-status-text {
    color: #dc2626;
}

/* 侧边栏顶部操作按钮 */
.sidebar-top-actions {
    padding: 16px;
    border-bottom: 1px solid #e5e7eb;
    flex-shrink: 0;
    display: flex;
    flex-direction: row;
    gap: 8px;
}

.sidebar-top-actions .sidebar-action-btn {
    flex: 1;
    padding: 10px 8px;
    border: none;
    border-radius: 6px;
    background: #6366f1;
    color: white;
    font-size: 11px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    position: relative;
    overflow: hidden;
    min-height: 36px;
}

.sidebar-top-actions .sidebar-action-btn:hover {
    background: #4f46e5;
    transform: translateY(-1px);
}

.sidebar-top-actions .sidebar-action-btn.direct-generate-btn {
    background: #6366f1;
}

.sidebar-top-actions .sidebar-action-btn.direct-generate-btn:hover {
    background: #4f46e5;
}

/* 带操作按钮的section标题 */
.section-header-with-action {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
}

.section-header-with-action .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #374151;
    margin: 0;
}

.section-action-btn {
    padding: 4px 8px;
    border: none;
    border-radius: 4px;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: white;
    font-size: 10px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 3px;
    min-height: 24px;
    box-shadow: 0 1px 2px rgba(99, 102, 241, 0.2);
}

.section-action-btn:hover {
    background: linear-gradient(135deg, #5b5bd6 0%, #7c3aed 100%);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(99, 102, 241, 0.3);
}

.section-action-btn .btn-icon {
    font-size: 12px;
}

.section-action-btn .btn-text {
    font-size: 12px;
}

/* 新建对话按钮 */
.new-conversation-section {
    padding: 16px;
    border-bottom: 1px solid #e1e5e9;
    flex-shrink: 0;
}

/* 统一的新对话按钮样式 */
.new-conversation-btn {
    width: auto;
    min-width: fit-content;
    padding: 6px 12px;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s;
    box-shadow: 0 2px 4px rgba(99, 102, 241, 0.3);
}

.new-conversation-btn:hover {
    background: linear-gradient(135deg, #5b5bd6 0%, #7c3aed 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(99, 102, 241, 0.4);
}

/* 移动端样式 */
@media (max-width: 768px) {
    .new-conversation-btn {
        width: 44px;
        height: 44px;
        border-radius: 12px;
        padding: 0;
    }

    .new-conversation-btn .btn-text {
        display: none;
    }
}





/* 区域标题 - 统一定义 */
.section-title {
    font-size: 16px;
    font-weight: 600;
    color: #6c757d;
    margin-bottom: 8px;
    padding: 0;
}

/* 对话历史区域 */
.conversation-history-section {
    height: 40%;
    border-bottom: 1px solid #e1e5e9;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    min-height: 0;
    padding: 16px;
}

.conversation-history-section .section-header-with-action {
    flex-shrink: 0;
    position: sticky;
    top: 0;
    background: #fff;
    z-index: 10;
    margin-bottom: 12px;
}

.conversation-history-section .section-title {
    flex-shrink: 0;
    position: sticky;
    top: 0;
    background: #fff;
    z-index: 10;
    border-bottom: 1px solid #f0f0f0;
}

.conversation-list {
    flex: 1;
    padding: 0 8px 16px;
    overflow-y: auto !important;
    min-height: 0;
    max-height: 100%;
    height: 100%;
}

.conversation-item {
    padding: 10px 12px;
    margin-bottom: 2px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
    position: relative;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 13px;
    min-height: 48px;
}

.conversation-item:hover {
    background: #f3f4f6;
}

.conversation-item.active {
    background: #e5e7eb;
    color: #374151;
    border-left: 3px solid #6366f1;
}

.conversation-icon {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    background: #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    flex-shrink: 0;
}

.conversation-item.active .conversation-icon {
    background: #6366f1;
    color: white;
}

.conversation-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 2px;
    overflow: hidden;
}

.conversation-title {
    font-size: 14px;
    font-weight: 600;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 1.2;
}

.conversation-timestamp {
    font-size: 11px;
    opacity: 0.7;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 1.2;
}

.conversation-delete-btn {
    width: 16px;
    height: 16px;
    border: none;
    background: none;
    color: #6c757d;
    cursor: pointer;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.2s;
    font-size: 11px;
}

.conversation-item:hover .conversation-delete-btn {
    opacity: 1;
}

.conversation-delete-btn:hover {
    background: #dc3545;
    color: white;
}

.conversation-item.active .conversation-delete-btn {
    color: #6b7280;
}

.conversation-item.active .conversation-delete-btn:hover {
    background: #dc3545;
    color: white;
}

/* 版本历史区域 */
.version-history-section {
    height: 40%;
    border-bottom: 1px solid #e1e5e9;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    min-height: 0; /* 确保flex子项可以收缩 */
    overflow: hidden;
    padding: 16px;
}

.version-history-section .section-header-with-action {
    flex-shrink: 0;
    position: sticky;
    top: 0;
    background: #fff;
    z-index: 10;
    margin-bottom: 12px;
}

.version-history-section .section-title {
    flex-shrink: 0;
    position: sticky;
    top: 0;
    background: #fff;
    z-index: 10;
    border-bottom: 1px solid #f0f0f0;
    font-size: 16px;
}

.version-list {
    flex: 1;
    padding: 0 8px 16px;
    overflow-y: auto !important;
    min-height: 0;
    max-height: 100%;
    height: 100%;
}



.version-item {
    padding: 10px 12px;
    margin-bottom: 2px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
}

.version-item:hover {
    background: #f3f4f6;
}

.version-item.active {
    background: #e5e7eb;
    color: #374151;
    border-left: 3px solid #6366f1;
}

.version-icon {
    width: 18px;
    height: 18px;
    border-radius: 3px;
    background: #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 9px;
    flex-shrink: 0;
}

.version-item.active .version-icon {
    background: #6366f1;
    color: white;
}

.version-info {
    flex: 1;
    overflow: hidden;
}

.version-name {
    font-size: 14px;
    font-weight: 600;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 1.3;
}

.version-meta {
    font-size: 11px;
    opacity: 0.7;
    margin-top: 1px;
}

.version-delete-btn {
    width: 16px;
    height: 16px;
    border: none;
    background: none;
    color: #6c757d;
    cursor: pointer;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.2s;
    font-size: 11px;
}

.version-item:hover .version-delete-btn {
    opacity: 1;
}

.version-delete-btn:hover {
    background: #dc3545;
    color: white;
}

.version-item.active .version-delete-btn {
    color: #6b7280;
}

.version-item.active .version-delete-btn:hover {
    background: #dc3545;
    color: white;
}

/* 版本操作区域 */
.version-actions {
    padding: 8px 12px;
    border-bottom: 1px solid #e1e5e9;
    background: #f8f9fa;
    flex-shrink: 0; /* 防止收缩 */
}

.add-version-btn {
    width: auto;
    min-width: fit-content;
    padding: 6px 12px;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s;
    box-shadow: 0 2px 4px rgba(99, 102, 241, 0.3);
}

.add-version-btn:hover {
    background: linear-gradient(135deg, #5b5bd6 0%, #7c3aed 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(99, 102, 241, 0.4);
}

.add-version-btn:active {
    transform: translateY(0);
}

.add-version-btn .btn-icon {
    font-size: 12px;
    font-weight: bold;
}

.add-version-btn .btn-text {
    font-size: 12px;
}

/* 版本内容区域（包含操作按钮和版本列表） */
.version-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; /* 关键：确保flex子项可以收缩 */
    overflow: hidden;
    max-height: calc(100% - 40px); /* 减去标题高度 */
}

/* 侧边栏底部操作按钮区域 - 更新时间: 2025-01-23 15:20 */
.unified-sidebar .sidebar-bottom-actions {
    padding: 12px !important;
    border-top: 1px solid #e1e5e9 !important;
    background: #f8f9fa !important;
    flex-shrink: 0 !important;
    display: flex !important;
    flex-direction: column !important;
    gap: 8px !important;
    position: static !important;
    z-index: auto !important;
    order: 999 !important;
    margin-top: auto !important;
}

.unified-sidebar .sidebar-bottom-actions .sidebar-action-btn {
    width: 100% !important;
    padding: 10px 12px !important;
    border: none !important;
    border-radius: 6px !important;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%) !important;
    color: white !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 6px !important;
    position: static !important;
    overflow: hidden !important;
    min-width: auto !important;
    transform: none !important;
    box-shadow: none !important;
    margin: 0 !important;
}

.unified-sidebar .sidebar-bottom-actions .sidebar-action-btn:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4) !important;
}

.sidebar-action-btn:active {
    transform: translateY(0);
}

.sidebar-action-btn.direct-generate-btn {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.sidebar-action-btn.direct-generate-btn:hover {
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.sidebar-action-btn .btn-icon {
    font-size: 14px;
}

.sidebar-action-btn .btn-text {
    font-size: 12px;
}

.sidebar-action-btn .btn-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: inherit;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.sidebar-action-btn .loading-spinner {
    width: 12px;
    height: 12px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}



/* 空状态 */
.no-conversations {
    padding: 20px 12px;
    text-align: center;
    color: #6c757d;
    font-size: 13px;
    font-style: italic;
}

.no-versions {
    padding: 16px 12px;
    text-align: center;
    color: #6c757d;
    font-size: 12px;
    font-style: italic;
}

/* 滚动条样式 */
.conversation-list::-webkit-scrollbar,
.version-list::-webkit-scrollbar {
    width: 4px;
}

.conversation-list::-webkit-scrollbar-track,
.version-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
}

.conversation-list::-webkit-scrollbar-thumb,
.version-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
}

.conversation-list::-webkit-scrollbar-thumb:hover,
.version-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 侧边栏遮罩层 */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    display: none;
}

/* 主内容区域调整 */
body {
    padding-left: 280px;
    transition: padding-left 0.3s ease;
}

body.sidebar-collapsed {
    padding-left: 0;
}

/* ==================== 响应式设计 ==================== */
/* 中等屏幕自动收起 */
@media (max-width: 1200px) {
    .unified-sidebar {
        transform: translateX(-280px);
    }

    .unified-sidebar.mobile-open {
        transform: translateX(0);
    }

    .sidebar-overlay.show {
        display: block;
    }

    body {
        padding-left: 0;
    }

    /* 显示悬浮的侧边栏切换按钮 */
    .floating-sidebar-toggle {
        position: fixed;
        top: 20px;
        left: 20px;
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 8px;
        color: white;
        cursor: pointer;
        display: none; /* 默认隐藏，通过JavaScript控制显示 */
        align-items: center;
        justify-content: center;
        z-index: 1001;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;
        font-size: 18px;
    }

    .floating-sidebar-toggle:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
    }

    .floating-sidebar-toggle:active {
        transform: scale(0.95);
    }
}

@media (max-width: 768px) {
    .unified-sidebar {
        width: 100%;
        max-width: 320px;
    }

    .floating-sidebar-toggle {
        width: 36px;
        height: 36px;
        top: 16px;
        left: 16px;
        font-size: 16px;
    }

    /* 移动端右侧导航调整 */
    .right-navigation {
        right: 10px;
        top: 60%;
    }

    .right-navigation.expanded {
        width: 160px;
    }
}

/* ==================== 动画效果 ==================== */
@keyframes slideInFromTop {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.version-tabs-container {
    animation: slideInFromTop 0.3s ease-out;
}

.conversation-history-panel {
    animation: slideInFromRight 0.3s ease-out;
}

.version-tab {
    animation: slideInFromTop 0.2s ease-out;
}

/* ==================== 工具提示样式 ==================== */
[title]:hover::before {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 9999;
    margin-bottom: 4px;
}

/* ==================== 状态指示器 ==================== */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
}

.status-indicator.active {
    background: #28a745;
    animation: pulse 2s infinite;
}

.status-indicator.inactive {
    background: #6c757d;
}

.status-indicator.error {
    background: #dc3545;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

/* ==================== 右侧章节导航 ==================== */
.right-navigation {
    position: fixed;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    background: white;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    z-index: 1000;
    overflow: visible;
    transition: all 0.3s ease;
    width: 70px;
    padding: 12px 0;
}

.nav-sections {
    padding: 0;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.nav-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px 6px;
    cursor: pointer;
    transition: all 0.2s;
    margin: 2px 8px;
    border-radius: 10px;
    position: relative;
}

.nav-section:hover {
    background: #f0f4ff;
    transform: translateY(-1px);
}

.nav-section.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    border: 2px solid rgba(255, 255, 255, 0.3);
    animation: activePulse 2s ease-in-out infinite alternate;
}

@keyframes activePulse {
    0% {
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    }
    100% {
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
    }
}

.section-label {
    font-size: 12px;
    font-weight: 600;
    color: #667eea;
    text-align: center;
    margin-bottom: 2px;
}

.nav-section.active .section-label {
    color: white;
}

.section-progress {
    font-size: 9px;
    color: #6c757d;
    font-weight: 500;
    background: rgba(255, 255, 255, 0.9);
    padding: 1px 4px;
    border-radius: 6px;
    min-width: 16px;
    text-align: center;
}

.nav-section.active .section-progress {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

/* 悬停时显示章节名称 */
.section-tooltip {
    position: absolute;
    right: 100%;
    top: 50%;
    transform: translateY(-50%);
    background: #333;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s;
    margin-right: 8px;
    z-index: 1001;
}

.section-tooltip::after {
    content: '';
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    border: 5px solid transparent;
    border-left-color: #333;
}

.nav-section:hover .section-tooltip {
    opacity: 1;
    visibility: visible;
}





/* ==================== 自定义确认对话框 ==================== */
.custom-confirm-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    animation: fadeIn 0.2s ease;
}

.custom-confirm-content {
    background: white;
    border-radius: 16px;
    padding: 32px;
    max-width: 420px;
    width: 90%;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    animation: slideIn 0.3s ease;
    position: relative;
}

.custom-confirm-close {
    position: absolute;
    top: 16px;
    right: 16px;
    background: none;
    border: none;
    font-size: 20px;
    color: #999;
    cursor: pointer;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s;
}

.custom-confirm-close:hover {
    background: #f5f5f5;
    color: #666;
}

.custom-confirm-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
}

.custom-confirm-icon {
    width: 40px;
    height: 40px;
    background: #ff6b35;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
    flex-shrink: 0;
}

.custom-confirm-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.custom-confirm-message {
    color: #666;
    margin-bottom: 32px;
    line-height: 1.5;
    font-size: 14px;
}

.custom-confirm-buttons {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.custom-confirm-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    min-width: 80px;
}

.cancel-btn {
    background: #f8f9fa;
    color: #666;
    border: 1px solid #e9ecef;
}

.cancel-btn:hover {
    background: #e9ecef;
}

.confirm-btn {
    background: #ff6b35;
    color: white;
}

.confirm-btn:hover {
    background: #e55a2b;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}
