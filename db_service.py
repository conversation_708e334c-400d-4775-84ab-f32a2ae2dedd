"""
数据库服务模块
提供对话、表单版本、聊天消息等数据的CRUD操作
与前端localStorage数据结构保持兼容
"""

from models import db, Conversation, FormVersion, ChatMessage
from flask import current_app
from flask_login import current_user
import uuid
import json
from datetime import datetime
from sqlalchemy import desc


class DatabaseService:
    """数据库服务类"""
    
    @staticmethod
    def create_conversation(title, description=None, form_data=None):
        """创建新对话"""
        conversation_id = str(uuid.uuid4())
        
        conversation = Conversation(
            id=conversation_id,
            user_id=current_user.id,
            title=title,
            description=description
        )
        
        db.session.add(conversation)
        
        # 如果有初始表单数据，创建第一个版本
        if form_data:
            version = DatabaseService.create_form_version(
                conversation_id=conversation_id,
                form_data=form_data,
                source_type='user',
                version_number=1,
                is_current=True
            )
        
        db.session.commit()
        
        current_app.logger.info(f"创建对话成功: {conversation_id}, 用户: {current_user.username}")
        return conversation
    
    @staticmethod
    def get_user_conversations(include_inactive=False):
        """获取当前用户的所有对话"""
        query = Conversation.query.filter_by(user_id=current_user.id)
        
        if not include_inactive:
            query = query.filter_by(is_active=True)
        
        conversations = query.order_by(
            desc(Conversation.updated_at)).all()
        return conversations
    
    @staticmethod
    def get_conversation(conversation_id):
        """获取指定对话详情"""
        conversation = Conversation.query.filter_by(
            id=conversation_id,
            user_id=current_user.id).first()
        
        return conversation
    
    @staticmethod
    def update_conversation(conversation_id, **kwargs):
        """更新对话信息"""
        conversation = DatabaseService.get_conversation(conversation_id)
        if not conversation:
            return None
        
        for key, value in kwargs.items():
            if hasattr(conversation, key):
                setattr(conversation, key, value)
        
        conversation.updated_at = datetime.utcnow()
        db.session.commit()
        
        return conversation
    
    @staticmethod
    def delete_conversation(conversation_id):
        """删除对话（软删除）"""
        conversation = DatabaseService.get_conversation(conversation_id)
        if not conversation:
            return False
        
        conversation.is_active = False
        conversation.updated_at = datetime.utcnow()
        db.session.commit()
        
        current_app.logger.info(f"删除对话: {conversation_id}, 用户: {current_user.username}")
        return True
    
    @staticmethod
    def create_form_version(conversation_id, form_data, source_type='user', 
                          source_version_id=None, modification_request=None, 
                          version_number=None, is_current=False):
        """创建表单版本"""
        version_id = str(uuid.uuid4())
        
        # 如果没有指定版本号，自动计算
        if version_number is None:
            max_version = db.session.query(
                db.func.max(FormVersion.version_number)
            ).filter_by(conversation_id=conversation_id).scalar() or 0
            version_number = max_version + 1
        
        # 如果设为当前版本，取消其他版本的current状态
        if is_current:
            FormVersion.query.filter_by(
                conversation_id=conversation_id,
                is_current=True
            ).update({'is_current': False})
        
        version = FormVersion(
            id=version_id,
            conversation_id=conversation_id,
            version_number=version_number,
            source_type=source_type,
            source_version_id=source_version_id,
            modification_request=modification_request,
            is_current=is_current
        )
        
        version.set_form_data(form_data)
        db.session.add(version)
        
        # 更新对话的更新时间
        conversation = Conversation.query.get(conversation_id)
        if conversation:
            conversation.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        current_app.logger.info(f"创建表单版本: {version_id}, 对话: {conversation_id}")
        return version
    
    @staticmethod
    def get_form_versions(conversation_id):
        """获取对话的所有表单版本"""
        versions = FormVersion.query.filter_by(
            conversation_id=conversation_id).order_by(
            FormVersion.version_number).all()
        
        return versions
    
    @staticmethod
    def get_form_version(version_id):
        """获取指定表单版本"""
        version = FormVersion.query.get(version_id)
        
        # 确保版本属于当前用户
        if version and version.conversation.user_id != current_user.id:
            return None
        
        return version
    
    @staticmethod
    def get_current_form_version(conversation_id):
        """获取对话的当前表单版本"""
        version = FormVersion.query.filter_by(
            conversation_id=conversation_id,
            is_current=True
        ).first()
        
        return version
    
    @staticmethod
    def set_current_form_version(version_id):
        """设置当前表单版本"""
        version = DatabaseService.get_form_version(version_id)
        if not version:
            return False
        
        # 取消同对话其他版本的current状态
        FormVersion.query.filter_by(
            conversation_id=version.conversation_id,
            is_current=True
        ).update({'is_current': False})
        
        # 设置当前版本
        version.is_current = True
        
        # 更新对话时间
        version.conversation.updated_at = datetime.utcnow()
        
        db.session.commit()
        return True
    
    @staticmethod
    def create_chat_message(conversation_id, sender, content, 
                          from_version_id=None, to_version_id=None):
        """创建聊天消息"""
        message_id = str(uuid.uuid4())
        
        message = ChatMessage(
            id=message_id,
            conversation_id=conversation_id,
            sender=sender,
            content=content,
            from_version_id=from_version_id,
            to_version_id=to_version_id
        )
        
        db.session.add(message)
        
        # 更新对话时间
        conversation = Conversation.query.get(conversation_id)
        if conversation:
            conversation.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        return message
    
    @staticmethod
    def get_chat_messages(conversation_id, limit=100):
        """获取对话的聊天消息"""
        messages = ChatMessage.query.filter_by(
            conversation_id=conversation_id
        ).order_by(ChatMessage.created_at).limit(limit).all()
        
        return messages
    
    @staticmethod
    def sync_from_frontend_data(frontend_data):
        """从前端数据同步到数据库"""
        try:
            synced_count = 0
            
            for conversation_data in frontend_data.get('conversations', []):
                conversation_id = conversation_data.get('id')
                
                # 检查对话是否已存在
                existing_conversation = Conversation.query.filter_by(
                    id=conversation_id,
                    user_id=current_user.id
                ).first()
                
                if not existing_conversation:
                    # 创建新对话
                    conversation = Conversation(
                        id=conversation_id,
                        user_id=current_user.id,
                        title=conversation_data.get('title', '未命名对话'),
                        description=conversation_data.get('description'),
                        created_at=datetime.fromisoformat(conversation_data['created_at'].replace('Z', '+00:00')) if conversation_data.get('created_at') else datetime.utcnow(),
                        updated_at=datetime.fromisoformat(conversation_data['updated_at'].replace('Z', '+00:00')) if conversation_data.get('updated_at') else datetime.utcnow()
                    )
                    db.session.add(conversation)
                    
                    # 同步表单版本
                    for version_data in conversation_data.get('form_versions', []):
                        version = FormVersion(
                            id=version_data['version_id'],
                            conversation_id=conversation_id,
                            version_number=version_data['version_number'],
                            source_type=version_data['source_type'],
                            source_version_id=version_data.get('source_version_id'),
                            modification_request=version_data.get('modification_request'),
                            created_at=datetime.fromisoformat(version_data['created_at'].replace('Z', '+00:00')) if version_data.get('created_at') else datetime.utcnow(),
                            is_current=version_data.get('is_current', False)
                        )
                        version.set_form_data(version_data['form_data'])
                        db.session.add(version)
                    
                    # 同步聊天消息
                    for message_data in conversation_data.get('chat_messages', []):
                        message = ChatMessage(
                            id=message_data['message_id'],
                            conversation_id=conversation_id,
                            sender=message_data['sender'],
                            content=message_data['content'],
                            from_version_id=message_data.get('from_version_id'),
                            to_version_id=message_data.get('to_version_id'),
                            created_at=datetime.fromisoformat(message_data['timestamp'].replace('Z', '+00:00')) if message_data.get('timestamp') else datetime.utcnow()
                        )
                        db.session.add(message)
                    
                    synced_count += 1
            
            db.session.commit()
            current_app.logger.info(f"同步前端数据成功: {synced_count}个对话, 用户: {current_user.username}")
            return synced_count
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"同步前端数据失败: {e}")
            raise e
    
    @staticmethod
    def export_to_frontend_format():
        """导出数据为前端兼容格式"""
        conversations = DatabaseService.get_user_conversations()
        
        result = {
            'conversations': [],
            'export_time': datetime.utcnow().isoformat(),
            'user_id': current_user.id
        }
        
        for conversation in conversations:
            conv_data = conversation.to_dict(include_versions=True, include_messages=True)
            result['conversations'].append(conv_data)
        
        return result
    
    @staticmethod
    def get_storage_stats():
        """获取存储统计信息"""
        user_id = current_user.id
        
        conversation_count = Conversation.query.filter_by(user_id=user_id, is_active=True).count()
        version_count = db.session.query(FormVersion).join(Conversation).filter(
            Conversation.user_id == user_id,
            Conversation.is_active.is_(True)
        ).count()
        message_count = db.session.query(ChatMessage).join(Conversation).filter(
            Conversation.user_id == user_id,
            Conversation.is_active.is_(True)
        ).count()
        
        return {
            'conversations': conversation_count,
            'form_versions': version_count,
            'chat_messages': message_count,
            'user_id': user_id,
            'last_updated': datetime.utcnow().isoformat()
        }