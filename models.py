"""
数据库模型定义
支持用户管理、对话记录、表单版本和聊天消息的持久化存储
"""

from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from datetime import datetime
import json

db = SQLAlchemy()


class User(UserMixin, db.Model):
    """用户模型"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=True)
    password_hash = db.Column(db.String(255), nullable=False)
    real_name = db.Column(db.String(100), nullable=True)
    department = db.Column(db.String(100), nullable=True)
    position = db.Column(db.String(100), nullable=True)
    is_active = db.Column(db.<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime, nullable=True)
    
    # 关联关系
    conversations = db.relationship('Conversation', backref='user', lazy=True, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<User {self.username}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'real_name': self.real_name,
            'department': self.department,
            'position': self.position,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None
        }


class Conversation(db.Model):
    """对话记录模型 - 对应前端的conversation数据结构"""
    __tablename__ = 'conversations'
    
    id = db.Column(db.String(36), primary_key=True)  # 使用UUID作为主键，与前端保持一致
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)
    
    # 关联关系
    form_versions = db.relationship('FormVersion', backref='conversation', lazy=True, cascade='all, delete-orphan')
    chat_messages = db.relationship('ChatMessage', backref='conversation', lazy=True, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<Conversation {self.id}: {self.title}>'
    
    def to_dict(self, include_versions=True, include_messages=True):
        """转换为字典格式，与前端数据结构兼容"""
        result = {
            'id': self.id,
            'title': self.title,
            'description': self.description,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'is_active': self.is_active,
            'user_id': self.user_id
        }
        
        if include_versions:
            result['form_versions'] = [version.to_dict() for version in self.form_versions]
        
        if include_messages:
            result['chat_messages'] = [message.to_dict() for message in self.chat_messages]
            
        return result


class FormVersion(db.Model):
    """表单版本模型 - 对应前端的formVersion数据结构"""
    __tablename__ = 'form_versions'
    
    id = db.Column(db.String(36), primary_key=True)  # version_id
    conversation_id = db.Column(db.String(36), db.ForeignKey('conversations.id'), nullable=False, index=True)
    version_number = db.Column(db.Integer, nullable=False)
    source_type = db.Column(db.String(20), nullable=False)  # 'user', 'ai'
    source_version_id = db.Column(db.String(36), nullable=True)  # 来源版本ID
    modification_request = db.Column(db.Text, nullable=True)  # 修改要求
    form_data = db.Column(db.Text, nullable=False)  # JSON格式的表单数据
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_current = db.Column(db.Boolean, default=False)  # 是否为当前活跃版本
    
    def __repr__(self):
        return f'<FormVersion {self.id}: v{self.version_number}>'
    
    def get_form_data(self):
        """获取解析后的表单数据"""
        try:
            return json.loads(self.form_data) if self.form_data else {}
        except json.JSONDecodeError:
            return {}
    
    def set_form_data(self, data):
        """设置表单数据"""
        self.form_data = json.dumps(data, ensure_ascii=False) if data else '{}'
    
    def to_dict(self):
        """转换为字典格式，与前端数据结构兼容"""
        return {
            'version_id': self.id,
            'version_number': self.version_number,
            'source_type': self.source_type,
            'source_version_id': self.source_version_id,
            'modification_request': self.modification_request,
            'form_data': self.get_form_data(),
            'created_at': self.created_at.isoformat(),
            'is_current': self.is_current
        }


class ChatMessage(db.Model):
    """聊天消息模型 - 对应前端的chatMessage数据结构"""
    __tablename__ = 'chat_messages'
    
    id = db.Column(db.String(36), primary_key=True)  # message_id
    conversation_id = db.Column(db.String(36), db.ForeignKey('conversations.id'), nullable=False, index=True)
    sender = db.Column(db.String(10), nullable=False)  # 'user' or 'ai'
    content = db.Column(db.Text, nullable=False)
    from_version_id = db.Column(db.String(36), nullable=True)  # 来源版本ID
    to_version_id = db.Column(db.String(36), nullable=True)  # 目标版本ID  
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<ChatMessage {self.id}: {self.sender}>'
    
    def to_dict(self):
        """转换为字典格式，与前端数据结构兼容"""
        return {
            'message_id': self.id,
            'sender': self.sender,
            'content': self.content,
            'from_version_id': self.from_version_id,
            'to_version_id': self.to_version_id,
            'timestamp': self.created_at.isoformat()
        }


# 数据库操作辅助函数
def init_db(app):
    """初始化数据库"""
    db.init_app(app)
    with app.app_context():
        db.create_all()
        print("数据库表创建完成")


def create_default_user():
    """创建默认管理员用户"""
    from werkzeug.security import generate_password_hash
    
    admin = User.query.filter_by(username='admin').first()
    if not admin:
        admin = User(
            username='admin',
            email='<EMAIL>',
            password_hash=generate_password_hash('admin123'),
            real_name='系统管理员',
            department='IT部门',
            position='管理员'
        )
        db.session.add(admin)
        db.session.commit()
        print("默认管理员用户创建完成 - 用户名: admin, 密码: admin123")
    return admin