"""
用户认证模块
处理用户登录、注册、会话管理等功能
"""

from flask import Blueprint, render_template, request, flash, redirect, url_for, jsonify
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.security import check_password_hash, generate_password_hash
from models import db, User
from forms import LoginForm, RegisterForm
from datetime import datetime

auth_bp = Blueprint('auth', __name__)


@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """用户登录"""
    if current_user.is_authenticated:
        return redirect(url_for('index'))
    
    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()
        
        if user and check_password_hash(user.password_hash, form.password.data):
            # 更新最后登录时间
            user.last_login = datetime.utcnow()
            db.session.commit()
            
            # 登录用户
            login_user(user, remember=form.remember_me.data)

            # 重定向到下一页或主页
            next_page = request.args.get('next')
            return redirect(next_page) if next_page else redirect(url_for('index'))
        else:
            flash('用户名或密码错误！', 'error')
    
    return render_template('auth/login.html', form=form)


@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    """用户注册"""
    if current_user.is_authenticated:
        return redirect(url_for('index'))
    
    form = RegisterForm()
    if form.validate_on_submit():
        # 检查用户名是否已存在
        existing_user = User.query.filter_by(username=form.username.data).first()
        if existing_user:
            flash('用户名已存在！', 'error')
            return render_template('auth/register.html', form=form)
        
        # 检查邮箱是否已存在
        if form.email.data:
            existing_email = User.query.filter_by(email=form.email.data).first()
            if existing_email:
                flash('邮箱已被注册！', 'error')
                return render_template('auth/register.html', form=form)
        
        # 创建新用户
        user = User(
            username=form.username.data,
            email=form.email.data,
            password_hash=generate_password_hash(form.password.data)
        )
        
        db.session.add(user)
        db.session.commit()
        
        flash('注册成功！请登录。', 'success')
        return redirect(url_for('auth.login'))
    
    return render_template('auth/register.html', form=form)


@auth_bp.route('/logout')
@login_required
def logout():
    """用户登出"""
    logout_user()
    flash('已成功登出！', 'info')
    return redirect(url_for('auth.login'))


@auth_bp.route('/api/user_info')
@login_required
def user_info():
    """获取当前用户信息 API"""
    return jsonify({
        'status': 'success',
        'user': current_user.to_dict()
    })


@auth_bp.route('/api/update_profile', methods=['POST'])
@login_required
def update_profile():
    """更新用户资料 API"""
    try:
        data = request.get_json()
        
        # 更新允许修改的字段
        if 'email' in data:
            # 检查邮箱是否已被其他用户使用
            existing_user = User.query.filter(User.email == data['email'], User.id != current_user.id).first()
            if existing_user:
                return jsonify({'status': 'error', 'message': '邮箱已被其他用户使用'}), 400
            current_user.email = data['email']
        
        db.session.commit()
        
        return jsonify({
            'status': 'success',
            'message': '资料更新成功',
            'user': current_user.to_dict()
        })
    
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'status': 'error',
            'message': f'更新失败：{str(e)}'
        }), 500