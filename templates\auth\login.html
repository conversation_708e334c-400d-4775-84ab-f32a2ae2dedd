{% extends "auth/base.html" %}

{% block title %}登录 - 8D报告协作系统{% endblock %}

{% block page_title %}用户登录{% endblock %}
{% block page_subtitle %}请输入您的用户名和密码{% endblock %}

{% block content %}
<form method="POST" novalidate>
    {{ form.hidden_tag() }}
    
    <div class="form-group">
        {{ form.username.label(class="form-label") }}
        {{ form.username(class="form-control" + (" error" if form.username.errors else "")) }}
        {% if form.username.errors %}
            {% for error in form.username.errors %}
                <div class="field-error">{{ error }}</div>
            {% endfor %}
        {% endif %}
    </div>
    
    <div class="form-group">
        {{ form.password.label(class="form-label") }}
        {{ form.password(class="form-control" + (" error" if form.password.errors else "")) }}
        {% if form.password.errors %}
            {% for error in form.password.errors %}
                <div class="field-error">{{ error }}</div>
            {% endfor %}
        {% endif %}
    </div>
    
    <div class="form-check">
        {{ form.remember_me(class="form-check-input") }}
        {{ form.remember_me.label(class="form-check-label") }}
    </div>
    
    {{ form.submit(class="btn btn-primary") }}
</form>

<div class="auth-links">
    <p>还没有账号？ <a href="{{ url_for('auth.register') }}">立即注册</a></p>
</div>

<script>
// 自动聚焦到第一个输入框
document.addEventListener('DOMContentLoaded', function() {
    const firstInput = document.querySelector('.form-control');
    if (firstInput) {
        firstInput.focus();
    }
});

// 回车键提交表单
document.addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        const submitBtn = document.querySelector('.btn-primary');
        if (submitBtn) {
            submitBtn.click();
        }
    }
});
</script>
{% endblock %}