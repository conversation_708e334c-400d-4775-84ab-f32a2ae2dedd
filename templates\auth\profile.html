{% extends "auth/base.html" %}

{% block title %}个人资料 - 8D报告协作系统{% endblock %}

{% block page_title %}个人资料{% endblock %}
{% block page_subtitle %}管理您的账号信息{% endblock %}

{% block content %}
<div class="profile-info">
    <div class="info-item">
        <label>用户名</label>
        <span>{{ user.username }}</span>
    </div>
    
    <div class="info-item">
        <label>真实姓名</label>
        <span>{{ user.real_name or '未设置' }}</span>
    </div>
    
    <div class="info-item">
        <label>邮箱</label>
        <span>{{ user.email or '未设置' }}</span>
    </div>
    
    <div class="info-item">
        <label>部门</label>
        <span>{{ user.department or '未设置' }}</span>
    </div>
    
    <div class="info-item">
        <label>职位</label>
        <span>{{ user.position or '未设置' }}</span>
    </div>
    
    <div class="info-item">
        <label>注册时间</label>
        <span>{{ user.created_at.strftime('%Y-%m-%d %H:%M') if user.created_at else '未知' }}</span>
    </div>
    
    <div class="info-item">
        <label>最后登录</label>
        <span>{{ user.last_login.strftime('%Y-%m-%d %H:%M') if user.last_login else '首次登录' }}</span>
    </div>
</div>

<div class="profile-actions">
    <a href="{{ url_for('index') }}" class="btn btn-primary">返回主页</a>
    <a href="{{ url_for('auth.logout') }}" class="btn btn-secondary">退出登录</a>
</div>

<style>
.profile-info {
    margin-bottom: 30px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #e1e5e9;
}

.info-item:last-child {
    border-bottom: none;
}

.info-item label {
    font-weight: 500;
    color: #333;
    font-size: 14px;
}

.info-item span {
    color: #666;
    font-size: 14px;
}

.profile-actions {
    display: flex;
    gap: 10px;
}

.btn-secondary {
    background: #6c757d;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

.btn-primary {
    flex: 1;
    margin-bottom: 10px;
}

@media (min-width: 480px) {
    .profile-actions {
        flex-direction: row;
    }
    
    .btn-primary {
        flex: 1;
        margin-bottom: 0;
    }
}
</style>
{% endblock %}